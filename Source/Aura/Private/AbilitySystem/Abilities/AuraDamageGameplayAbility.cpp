// Copyright EclipseStudio

/**
 * @file AuraDamageGameplayAbility.cpp
 * @brief Aura伤害游戏能力系统实现
 *
 * 本文件实现了所有造成伤害的游戏能力的基础功能，包括：
 * - 伤害计算与等级缩放
 * - 游戏效果参数配置与应用
 * - 减益状态（Debuff）处理
 * - 击退与死亡冲击力计算
 * - 径向伤害支持
 * - 动画蒙太奇随机选择
 *
 * 该类作为所有伤害类技能的基类，提供统一的伤害处理接口，
 * 确保伤害系统的一致性和可扩展性。
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024
 */

#include "AbilitySystem/Abilities/AuraDamageGameplayAbility.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AbilitySystemComponent.h"

/**
 * @brief 对目标角色造成伤害
 *
 * 该方法创建并应用伤害游戏效果到指定目标。伤害值会根据当前能力等级进行缩放，
 * 并通过GameplayTag系统设置伤害类型，确保伤害计算的准确性。
 *
 * @param TargetActor 目标角色，必须具有有效的AbilitySystemComponent
 *
 * @note 该方法假设目标角色已经通过碰撞检测或其他方式确定为有效目标
 * @warning 如果TargetActor为nullptr或缺少ASC，可能导致崩溃
 *
 * @see MakeDamageEffectParamsFromClassDefaults() 获取更复杂的伤害参数配置
 */
void UAuraDamageGameplayAbility::CauseDamage(AActor* TargetActor)
{
	// 创建伤害效果规格句柄，等级设为1.0（实际等级通过ScaledDamage处理）
	FGameplayEffectSpecHandle DamageSpecHandle = MakeOutgoingGameplayEffectSpec(DamageEffectClass, 1.f);

	// 根据当前能力等级计算缩放后的伤害值
	const float ScaledDamage = Damage.GetValueAtLevel(GetAbilityLevel());

	// 通过SetByCaller机制设置伤害类型和数值，允许运行时动态调整
	UAbilitySystemBlueprintLibrary::AssignTagSetByCallerMagnitude(DamageSpecHandle, DamageType, ScaledDamage);

	// 将伤害效果应用到目标的能力系统组件
	GetAbilitySystemComponentFromActorInfo()->ApplyGameplayEffectSpecToTarget(
		*DamageSpecHandle.Data.Get(),
		UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(TargetActor)
	);
}

/**
 * @brief 从类默认值创建完整的伤害效果参数结构
 *
 * 该方法构建一个包含所有伤害相关参数的FDamageEffectParams结构体，
 * 支持多种伤害类型和效果的配置，包括基础伤害、减益效果、击退力、
 * 死亡冲击力以及径向伤害等。
 *
 * @param TargetActor 目标角色，用于计算方向向量和获取ASC
 * @param InRadialDamageOrigin 径向伤害的原点位置
 * @param bOverrideKnockbackDirection 是否覆盖默认击退方向
 * @param KnockbackDirectionOverride 自定义击退方向向量
 * @param bOverrideDeathImpulse 是否覆盖默认死亡冲击方向
 * @param DeathImpulseDirectionOverride 自定义死亡冲击方向向量
 * @param bOverridePitch 是否覆盖俯仰角
 * @param PitchOverride 自定义俯仰角度值
 *
 * @return FDamageEffectParams 配置完整的伤害效果参数结构体
 *
 * @note 该方法支持灵活的参数覆盖机制，允许特定技能自定义伤害表现
 * @see FDamageEffectParams 查看完整的参数结构定义
 */
FDamageEffectParams UAuraDamageGameplayAbility::MakeDamageEffectParamsFromClassDefaults(AActor* TargetActor,
	FVector InRadialDamageOrigin, bool bOverrideKnockbackDirection, FVector KnockbackDirectionOverride,
	bool bOverrideDeathImpulse, FVector DeathImpulseDirectionOverride, bool bOverridePitch, float PitchOverride) const
{
	FDamageEffectParams Params;

	// === 基础参数配置 ===
	Params.WorldContextObject = GetAvatarActorFromActorInfo();
	Params.DamageGameplayEffectClass = DamageEffectClass;
	Params.SourceAbilitySystemComponent = GetAbilitySystemComponentFromActorInfo();
	Params.TargetAbilitySystemComponent = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(TargetActor);

	// === 伤害相关参数 ===
	Params.BaseDamage = Damage.GetValueAtLevel(GetAbilityLevel());  // 根据等级缩放的基础伤害
	Params.AbilityLevel = GetAbilityLevel();                        // 当前能力等级
	Params.DamageType = DamageType;                                 // 伤害类型标签

	// === 减益效果参数 ===
	Params.DebuffChance = DebuffChance;         // 减益触发概率（0-100%）
	Params.DebuffDamage = DebuffDamage;         // 减益持续伤害值
	Params.DebuffDuration = DebuffDuration;     // 减益持续时间（秒）
	Params.DebuffFrequency = DebuffFrequency;   // 减益伤害频率（秒/次）

	// === 物理效果参数 ===
	Params.DeathImpulseMagnitude = DeathImpulseMagnitude;   // 死亡冲击力大小
	Params.KnockbackForceMagnitude = KnockbackForceMagnitude; // 击退力大小
	Params.KnockbackChance = KnockbackChance;               // 击退触发概率

	// === 方向向量计算 ===
	// 当目标角色有效时，计算从施法者到目标的方向向量
	if (IsValid(TargetActor))
	{
		// 计算从施法者到目标的方向向量，并转换为旋转角度
		FRotator Rotation = (TargetActor->GetActorLocation() - GetAvatarActorFromActorInfo()->GetActorLocation()).Rotation();

		// 如果需要覆盖俯仰角，应用自定义俯仰角度
		if (bOverridePitch)
		{
			Rotation.Pitch = PitchOverride;
		}

		// 将旋转角度转换回方向向量，用于后续的力计算
		const FVector ToTarget = Rotation.Vector();

		// 如果未指定自定义击退方向，使用计算出的目标方向
		if (!bOverrideKnockbackDirection)
		{
			Params.KnockbackForce = ToTarget * KnockbackForceMagnitude;
		}

		// 如果未指定自定义死亡冲击方向，使用计算出的目标方向
		if (!bOverrideDeathImpulse)
		{
			Params.DeathImpulse = ToTarget * DeathImpulseMagnitude;
		}
	}

	// === 自定义击退方向处理 ===
	// 当需要覆盖默认击退方向时，使用提供的自定义方向向量
	if (bOverrideKnockbackDirection)
	{
		// 标准化方向向量，确保方向的一致性
		KnockbackDirectionOverride.Normalize();
		Params.KnockbackForce = KnockbackDirectionOverride * KnockbackForceMagnitude;

		// 如果同时需要覆盖俯仰角，重新计算击退力向量
		if (bOverridePitch)
		{
			FRotator KnockbackRotation = KnockbackDirectionOverride.Rotation();
			KnockbackRotation.Pitch = PitchOverride;
			Params.KnockbackForce = KnockbackRotation.Vector() * KnockbackForceMagnitude;
		}
	}

	// === 自定义死亡冲击方向处理 ===
	// 当需要覆盖默认死亡冲击方向时，使用提供的自定义方向向量
	if (bOverrideDeathImpulse)
	{
		// 标准化方向向量，确保冲击力方向的一致性
		DeathImpulseDirectionOverride.Normalize();
		Params.DeathImpulse = DeathImpulseDirectionOverride * DeathImpulseMagnitude;

		// 如果同时需要覆盖俯仰角，重新计算死亡冲击力向量
		if (bOverridePitch)
		{
			FRotator DeathImpulseRotation = DeathImpulseDirectionOverride.Rotation();
			DeathImpulseRotation.Pitch = PitchOverride;
			Params.DeathImpulse = DeathImpulseRotation.Vector() * DeathImpulseMagnitude;
		}
	}

	// === 径向伤害配置 ===
	// 如果该技能支持径向伤害（如爆炸、冲击波等），配置相关参数
	if (bIsRadialDamage)
	{
		Params.bIsRadialDamage = bIsRadialDamage;                           // 启用径向伤害标志
		Params.RadialDamageOrigin = InRadialDamageOrigin;                   // 伤害原点位置
		Params.RadialDamageInnerRadius = RadialDamageInnerRadius;           // 内半径（满伤害区域）
		Params.RadialDamageOuterRadius = RadialDamageOuterRadius;           // 外半径（伤害衰减边界）
	}

	return Params;
}

/**
 * @brief 获取当前等级下的伤害值
 *
 * 该方法根据能力的当前等级计算并返回对应的伤害值。
 * 使用FScalableFloat系统，支持基于等级的伤害缩放。
 *
 * @return float 当前等级下的伤害值
 *
 * @note 该方法为const函数，不会修改对象状态
 * @see FScalableFloat::GetValueAtLevel() 查看缩放计算逻辑
 */
float UAuraDamageGameplayAbility::GetDamageAtLevel() const
{
	return Damage.GetValueAtLevel(GetAbilityLevel());
}

/**
 * @brief 从动画蒙太奇数组中随机选择一个
 *
 * 该方法用于为技能随机选择攻击动画，增加战斗的多样性和视觉效果。
 * 每次调用都会从提供的数组中随机选择一个FTaggedMontage结构体。
 *
 * @param TaggedMontages 包含标签动画的数组，每个元素包含动画、标签和音效信息
 * @return FTaggedMontage 随机选择的标签动画结构体，如果数组为空则返回默认值
 *
 * @note 使用FMath::RandRange确保随机分布的均匀性
 * @warning 如果传入空数组，将返回默认构造的FTaggedMontage
 *
 * @see FTaggedMontage 查看标签动画结构体的完整定义
 */
FTaggedMontage UAuraDamageGameplayAbility::GetRandomTaggedMontageFromArray(const TArray<FTaggedMontage>& TaggedMontages) const
{
	// 检查数组是否包含有效元素
	if (TaggedMontages.Num() > 0)
	{
		// 生成随机索引，范围为[0, 数组长度-1]
		const int32 Selection = FMath::RandRange(0, TaggedMontages.Num() - 1);
		return TaggedMontages[Selection];
	}

	// 如果数组为空，返回默认构造的FTaggedMontage
	return FTaggedMontage();
}
