// Copyright EclipseStudio

#pragma once

#include "CoreMinimal.h"
#include "AuraAbilityTypes.h"
#include "AbilitySystem/Abilities/AuraGameplayAbility.h"
#include "Interaction/CombatInterface.h"
#include "AuraDamageGameplayAbility.generated.h"

/**
 * @class UAuraDamageGameplayAbility
 * @brief Aura伤害游戏能力基类
 *
 * 该类作为所有造成伤害的游戏能力的基础类，提供统一的伤害处理接口。
 * 支持多种伤害类型和效果，包括：
 * - 基础伤害计算与等级缩放
 * - 减益状态（Debuff）系统
 * - 击退与死亡冲击力
 * - 径向伤害支持
 * - 动画蒙太奇管理
 *
 * 继承自UAuraGameplayAbility，集成了Aura项目的能力系统框架。
 * 所有具体的伤害技能（如火球术、闪电链等）都应继承此类。
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
UCLASS()
class AURA_API UAuraDamageGameplayAbility : public UAuraGameplayAbility
{
	GENERATED_BODY()

public:
	// ========================================
	// 公共接口方法
	// ========================================

	/**
	 * @brief 对目标角色造成伤害
	 *
	 * 创建并应用伤害游戏效果到指定目标，伤害值根据当前能力等级自动缩放。
	 *
	 * @param TargetActor 目标角色，必须具有有效的AbilitySystemComponent
	 */
	UFUNCTION(BlueprintCallable, Category = "Damage", meta = (CallInEditor = "false"))
	void CauseDamage(AActor* TargetActor);

	/**
	 * @brief 从类默认值创建完整的伤害效果参数结构
	 *
	 * 构建包含所有伤害相关参数的FDamageEffectParams结构体，支持灵活的参数覆盖。
	 * 该方法允许技能在运行时动态调整伤害表现，如自定义击退方向、死亡冲击等。
	 *
	 * @param TargetActor 目标角色，用于计算方向向量
	 * @param InRadialDamageOrigin 径向伤害的原点位置
	 * @param bOverrideKnockbackDirection 是否覆盖默认击退方向
	 * @param KnockbackDirectionOverride 自定义击退方向向量
	 * @param bOverrideDeathImpulse 是否覆盖默认死亡冲击方向
	 * @param DeathImpulseDirectionOverride 自定义死亡冲击方向向量
	 * @param bOverridePitch 是否覆盖俯仰角
	 * @param PitchOverride 自定义俯仰角度值
	 * @return FDamageEffectParams 配置完整的伤害效果参数结构体
	 */
	UFUNCTION(BlueprintPure, Category = "Damage", meta = (CallInEditor = "false"))
	FDamageEffectParams MakeDamageEffectParamsFromClassDefaults(
		AActor* TargetActor = nullptr,
		FVector InRadialDamageOrigin = FVector::ZeroVector,
		bool bOverrideKnockbackDirection = false,
		FVector KnockbackDirectionOverride = FVector::ZeroVector,
		bool bOverrideDeathImpulse = false,
		FVector DeathImpulseDirectionOverride = FVector::ZeroVector,
		bool bOverridePitch = false,
		float PitchOverride = 0.f) const;

	/**
	 * @brief 获取当前等级下的伤害值
	 *
	 * 根据能力的当前等级计算并返回对应的伤害值，使用FScalableFloat系统进行缩放。
	 *
	 * @return float 当前等级下的伤害值
	 */
	UFUNCTION(BlueprintPure, Category = "Damage", meta = (CallInEditor = "false"))
	float GetDamageAtLevel() const;
protected:
	// ========================================
	// 核心伤害配置
	// ========================================

	/**
	 * @brief 伤害效果类
	 *
	 * 定义应用到目标的GameplayEffect类型，包含伤害计算逻辑和属性修改规则。
	 * 该效果类决定了伤害如何影响目标的生命值和其他属性。
	 */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Core Damage",
		meta = (DisplayName = "Damage Effect Class"))
	TSubclassOf<UGameplayEffect> DamageEffectClass;

	/**
	 * @brief 伤害类型标签
	 *
	 * 用于标识伤害的元素类型（如火焰、冰霜、闪电等），影响伤害计算和抗性检查。
	 * 该标签与游戏的元素系统和抗性系统紧密关联。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Core Damage",
		meta = (DisplayName = "Damage Type", Categories = "Damage"))
	FGameplayTag DamageType;

	/**
	 * @brief 基础伤害值（支持等级缩放）
	 *
	 * 使用FScalableFloat系统，支持基于能力等级的伤害缩放。
	 * 可以通过曲线表或简单的线性公式定义伤害随等级的变化。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Core Damage",
		meta = (DisplayName = "Base Damage"))
	FScalableFloat Damage;

	// ========================================
	// 减益效果配置
	// ========================================

	/**
	 * @brief 减益触发概率（百分比）
	 *
	 * 定义该技能触发减益状态的概率，范围0-100%。
	 * 减益效果包括持续伤害、移动速度降低等负面状态。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Debuff Effects",
		meta = (ClampMin = "0.0", ClampMax = "100.0", DisplayName = "Debuff Chance (%)", UIMin = "0", UIMax = "100"))
	float DebuffChance = 20.f;

	/**
	 * @brief 减益持续伤害值
	 *
	 * 减益状态每次触发时造成的伤害量，通常低于主要伤害但持续时间较长。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Debuff Effects",
		meta = (ClampMin = "0.0", DisplayName = "Debuff Damage", UIMin = "0"))
	float DebuffDamage = 5.f;

	/**
	 * @brief 减益伤害触发频率（秒）
	 *
	 * 减益状态造成伤害的时间间隔，值越小伤害频率越高。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Debuff Effects",
		meta = (ClampMin = "0.1", DisplayName = "Debuff Frequency (s)", UIMin = "0.1"))
	float DebuffFrequency = 1.f;

	/**
	 * @brief 减益持续时间（秒）
	 *
	 * 减益状态的总持续时间，超过此时间后减益效果自动消失。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Debuff Effects",
		meta = (ClampMin = "0.1", DisplayName = "Debuff Duration (s)", UIMin = "0.1"))
	float DebuffDuration = 5.f;

	// ========================================
	// 物理效果配置
	// ========================================

	/**
	 * @brief 死亡冲击力大小
	 *
	 * 当目标死亡时施加的物理冲击力大小，用于创建死亡时的物理反馈效果。
	 * 较大的值会产生更明显的尸体飞行效果。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Physical Effects",
		meta = (ClampMin = "0.0", DisplayName = "Death Impulse Magnitude", UIMin = "0"))
	float DeathImpulseMagnitude = 1000.f;

	/**
	 * @brief 击退力大小
	 *
	 * 对目标施加的击退力大小，用于创建技能的冲击感。
	 * 配合击退概率使用，可以实现随机的击退效果。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Physical Effects",
		meta = (ClampMin = "0.0", DisplayName = "Knockback Force Magnitude", UIMin = "0"))
	float KnockbackForceMagnitude = 1000.f;

	/**
	 * @brief 击退触发概率
	 *
	 * 技能造成击退效果的概率，0表示不会击退，1表示必定击退。
	 * 可以用于平衡技能的控制能力。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Physical Effects",
		meta = (ClampMin = "0.0", ClampMax = "1.0", DisplayName = "Knockback Chance", UIMin = "0", UIMax = "1"))
	float KnockbackChance = 0.f;

	// ========================================
	// 径向伤害配置
	// ========================================

	/**
	 * @brief 是否启用径向伤害
	 *
	 * 启用后，技能将对指定范围内的所有目标造成伤害，如爆炸、冲击波等效果。
	 * 径向伤害通常具有内外半径，内半径内为满伤害，外半径边缘伤害衰减。
	 */
	UPROPERTY(EditDefaultsOnly, Category = "Radial Damage",
		meta = (DisplayName = "Enable Radial Damage"))
	bool bIsRadialDamage = false;

	/**
	 * @brief 径向伤害内半径
	 *
	 * 径向伤害的内半径，在此范围内的目标受到满额伤害。
	 * 内半径应小于或等于外半径。
	 */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Radial Damage",
		meta = (ClampMin = "0.0", DisplayName = "Inner Radius", UIMin = "0", EditCondition = "bIsRadialDamage"))
	float RadialDamageInnerRadius = 0.f;

	/**
	 * @brief 径向伤害外半径
	 *
	 * 径向伤害的外半径，超出此范围的目标不受伤害影响。
	 * 在内外半径之间的目标受到衰减伤害。
	 */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Radial Damage",
		meta = (ClampMin = "0.0", DisplayName = "Outer Radius", UIMin = "0", EditCondition = "bIsRadialDamage"))
	float RadialDamageOuterRadius = 0.f;

	// ========================================
	// 动画系统辅助方法
	// ========================================

	/**
	 * @brief 从标签动画数组中随机选择一个
	 *
	 * 用于为技能随机选择攻击动画，增加战斗的多样性和视觉效果。
	 * 支持带有标签、插槽和音效信息的完整动画配置。
	 *
	 * @param TaggedMontages 包含标签动画的数组
	 * @return FTaggedMontage 随机选择的标签动画，如果数组为空则返回默认值
	 */
	UFUNCTION(BlueprintPure, Category = "Animation",
		meta = (CallInEditor = "false", DisplayName = "Get Random Tagged Montage"))
	FTaggedMontage GetRandomTaggedMontageFromArray(const TArray<FTaggedMontage>& TaggedMontages) const;
};
